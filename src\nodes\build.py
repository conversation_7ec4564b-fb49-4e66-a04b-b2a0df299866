from src.models.state import <PERSON>stado

def node_build(state: Estado) -> Estado:
    plan_impl = [
        "Crear app FastAPI y endpoint /webhook",
        "Cliente Sheets y escritura segura",
        "Pruebas unitarias (Pytest)",
    ]
    artefactos = {
        "paths": ["src/app.py", "src/nodes/*", "tests/*"],
        "plan_vibe": plan_impl
    }

    tldr = state.get("tldr", [])
    tldr.append("Construcción: plan de implementación y artefactos listos")

    return {**state, "artefactos": artefactos, "tldr": tldr}