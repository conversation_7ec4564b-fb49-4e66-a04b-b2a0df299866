#!/usr/bin/env python3

import requests
import json
import sys

def make_request():
    url = "http://127.0.0.1:8000/api/run"
    payload = {
        "idea": "Genera una Landing Page de <PERSON> t<PERSON>, gótica, impactante, que promocione todas las películas de <PERSON> de manera mágica"
    }
    
    try:
        print("Making POST request to /api/run...")
        response = requests.post(url, json=payload, timeout=180)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            print("\n=== RESPONSE ===")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            return result
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print("Response text:", response.text)
            return None
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return None
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the server running?")
        return None
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    result = make_request()
    
    if result:
        print("\n=== STATE SUMMARY ===")
        state = result.get("state", {})
        for key, value in state.items():
            if isinstance(value, (dict, list)):
                print(f"{key}: {type(value).__name__} with {len(value)} items")
            else:
                print(f"{key}: {str(value)[:100]}...")
