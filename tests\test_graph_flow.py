from src.graph.graph import build_graph
from src.models.state import INITIAL_STATE

def test_flow_tldr_sequence():
    app = build_graph()
    out = app.invoke({**INITIAL_STATE, "idea": "Demo idea"})
    tldr = out.get("tldr", [])
    assert any("Intake" in x for x in tldr)
    assert any("Diseño" in x for x in tldr)
    assert any("Arquitectura" in x for x in tldr)
    assert any("Construcción" in x for x in tldr)
    assert any("Lanzamiento" in x for x in tldr)