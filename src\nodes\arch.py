from src.models.state import <PERSON>stad<PERSON>

def node_arch(state: Estado) -> Estado:
    arquitectura = {
        "paradigma": "Monolito modular (FastAPI)",
        "contratos": {"api": ["POST /webhook", "POST /api/run"], "datos": ["sheets: reservas"]},
        "adrs": [
            {
                "id": "ADR-001",
                "decision": "Monolito modular",
                "justificacion": "Menor complejidad y costo para MVP"
            }
        ]
    }

    tldr = state.get("tldr", [])
    tldr.append("Arquitectura mínima definida (ADR-001)")

    return {**state, "arquitectura": arquitectura, "tldr": tldr}