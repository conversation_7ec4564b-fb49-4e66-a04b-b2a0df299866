<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SAL-3-2 State Visualizer</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .content {
            padding: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .section h2 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        .idea { background: linear-gradient(135deg, #ff9a9e, #fecfef); }
        .constitucion { background: linear-gradient(135deg, #a8edea, #fed6e3); }
        .prototipo { background: linear-gradient(135deg, #ffecd2, #fcb69f); }
        .arquitectura { background: linear-gradient(135deg, #d299c2, #fef9d7); }
        .tldr { background: linear-gradient(135deg, #89f7fe, #66a6ff); }
        
        .json-viewer {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .controls {
            text-align: center;
            margin-bottom: 30px;
        }
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .status {
            text-align: center;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.loading {
            background: #fff3cd;
            color: #856404;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 SAL-3-2 State Visualizer</h1>
            <p>Sistema de Automatización de Lanzamiento - Versión 3.2</p>
        </div>
        
        <div class="content">
            <div class="controls">
                <button class="btn" onclick="loadState()">🔄 Cargar Estado</button>
                <button class="btn" onclick="runNewIdea()">💡 Nueva Idea</button>
            </div>
            
            <div id="status" class="status" style="display: none;"></div>
            
            <div id="stateContent"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000';
        
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
        }
        
        function hideStatus() {
            document.getElementById('status').style.display = 'none';
        }
        
        async function loadState() {
            showStatus('Cargando estado...', 'loading');
            try {
                const response = await fetch(`${API_BASE}/api/state`);
                const data = await response.json();
                displayState(data.state);
                showStatus('Estado cargado exitosamente', 'success');
                setTimeout(hideStatus, 3000);
            } catch (error) {
                showStatus('Error al cargar el estado', 'error');
                console.error('Error:', error);
            }
        }
        
        function displayState(state) {
            const content = document.getElementById('stateContent');
            content.innerHTML = '';
            
            if (!state || Object.keys(state).length === 0) {
                content.innerHTML = '<p>No hay estado disponible. Ejecuta una nueva idea primero.</p>';
                return;
            }
            
            // Idea
            if (state.idea) {
                content.innerHTML += `
                    <div class="section idea">
                        <h2>💡 Idea Original</h2>
                        <p><strong>${state.idea}</strong></p>
                    </div>
                `;
            }
            
            // TLDR
            if (state.tldr && state.tldr.length > 0) {
                content.innerHTML += `
                    <div class="section tldr">
                        <h2>📋 Resumen Ejecutivo (TLDR)</h2>
                        <ul>
                            ${state.tldr.map(item => `<li>${item}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }
            
            // Constitución
            if (state.constitucion) {
                content.innerHTML += `
                    <div class="section constitucion">
                        <h2>🎯 Constitución del Proyecto</h2>
                        <div class="json-viewer">${JSON.stringify(state.constitucion, null, 2)}</div>
                    </div>
                `;
            }
            
            // Prototipo
            if (state.prototipo) {
                content.innerHTML += `
                    <div class="section prototipo">
                        <h2>🎨 Prototipo y Diseño</h2>
                        <div class="json-viewer">${JSON.stringify(state.prototipo, null, 2)}</div>
                    </div>
                `;
            }
            
            // Arquitectura
            if (state.arquitectura) {
                content.innerHTML += `
                    <div class="section arquitectura">
                        <h2>🏗️ Arquitectura</h2>
                        <div class="json-viewer">${JSON.stringify(state.arquitectura, null, 2)}</div>
                    </div>
                `;
            }
        }
        
        async function runNewIdea() {
            const idea = prompt('Ingresa tu nueva idea:');
            if (!idea) return;
            
            showStatus('Procesando idea...', 'loading');
            try {
                const response = await fetch(`${API_BASE}/api/run`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ idea: idea })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    displayState(data.state);
                    showStatus('Idea procesada exitosamente', 'success');
                    setTimeout(hideStatus, 3000);
                } else {
                    showStatus('Error al procesar la idea', 'error');
                }
            } catch (error) {
                showStatus('Error de conexión', 'error');
                console.error('Error:', error);
            }
        }
        
        // Cargar estado al iniciar
        window.onload = loadState;
    </script>
</body>
</html>
