#!/usr/bin/env python3

import sys
import traceback
from src.models.state import INITIAL_STATE
from src.graph.graph import build_graph

def test_graph():
    try:
        print("Building graph...")
        graph = build_graph()
        print("Graph built successfully!")
        
        print("Testing with sample state...")
        test_state = {
            **INITIAL_STATE, 
            "idea": "Genera una Landing Page de <PERSON> t<PERSON>, gótica, impactante, que promocione todas las películas de <PERSON> de manera mágica"
        }
        
        print("Invoking graph...")
        config = {"configurable": {"thread_id": "test-thread"}}
        result = graph.invoke(test_state, config=config)
        print("Graph execution completed!")
        print("Result keys:", list(result.keys()))
        
        return result
        
    except Exception as e:
        print(f"Error: {e}")
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = test_graph()
    if result:
        print("\n=== FINAL STATE ===")
        import json
        print(json.dumps(result, indent=2, ensure_ascii=False))
