from src.models.state import Estado

def node_design(state: Estado) -> Estado:
    idea = state.get("idea")
    constitucion = state.get("constitucion", {})

    personas = [
        {"nombre": "Freelancer", "jobs": ["agendar", "confirmar"], "dolores": ["no-show"]},
        {"nombre": "Academia", "jobs": ["gestionar cupos"], "dolores": ["seguimiento manual"]},
    ]
    journey = ["formulario→evento GCal", "webhook→Sheets", "mensaje bienvenida"]

    prototipo = {
        "personas": personas,
        "journey": journey,
        "core_copy": {
            "hero": "Agenda confirmada en segundos",
            "cta": "Verificar reserva",
            "error": "Revisa los datos e inténtalo de nuevo"
        }
    }

    backlog = {
        "epicas": ["Captura reservas", "Notificación"],
        "hu": [
            {
                "como": "Freelancer",
                "quiero": "registrar una reserva en Sheets",
                "para": "centralizar y confirmar",
                "criterios": [
                    "Dado un webhook válido, <PERSON><PERSON><PERSON> llega al endpoint, Entonces se escribe fila con campos X/Y/Z"
                ]
            }
        ]
    }

    tldr = state.get("tldr", [])
    tldr.append("Diseño: personas, journey, core copy y backlog creados")

    return {**state, "prototipo": prototipo, "backlog": backlog, "tldr": tldr}