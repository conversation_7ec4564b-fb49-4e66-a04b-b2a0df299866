from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from src.models.state import Estado, INITIAL_STATE
from src.nodes.intake import node_intake
from src.nodes.design import node_design
from src.nodes.arch import node_arch
from src.nodes.build import node_build
from src.nodes.qa_sec import node_qa_sec
from src.nodes.launch import node_launch
from src.nodes.data_gtm import node_data_gtm

def build_graph():
    workflow = StateGraph(Estado)
    workflow.add_node("intake", node_intake)
    workflow.add_node("design", node_design)
    workflow.add_node("arch", node_arch)
    workflow.add_node("build", node_build)
    workflow.add_node("qa_sec", node_qa_sec)
    workflow.add_node("launch", node_launch)
    workflow.add_node("data_gtm", node_data_gtm)

    workflow.set_entry_point("intake")
    workflow.add_edge("intake", "design")
    workflow.add_edge("design", "arch")
    workflow.add_edge("arch", "build")

    def qa_route(state: Estado):
        rpt = state.get("reporte_qa", {})
        if rpt.get("bloqueante"):
            return "build"
        return "launch"

    workflow.add_edge("build", "qa_sec")
    workflow.add_conditional_edges("qa_sec", qa_route, {"build": "build", "launch": "launch"})
    workflow.add_edge("launch", "data_gtm")
    workflow.add_edge("data_gtm", END)

    checkpointer = MemorySaver()
    app = workflow.compile(checkpointer=checkpointer)
    return app