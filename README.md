# SAL-3+2 — Sistema Agéntico LEAN (LangGraph + FastAPI)

**Objetivo**: llevar una idea → MVP → lanzamiento con **3 roles núcleo** (Orquestador con estado, <PERSON>se<PERSON><PERSON> de Producto, Constructor) y **2 modos** activables como *gates* (QA&Seguridad, Datos&Go-To-Market).

## 1. Quickstart

```bash
python -m venv .venv && source .venv/bin/activate  # Windows: .venv\Scripts\activate
pip install -r requirements.txt
cp .env.sample .env
uvicorn src.app:app --reload --port 8000
```

- **Endpoint** principal: `POST /api/run` → ejecuta el flujo completo (intake → diseño → arquitectura mínima → construcción → QA/Gate → lanzamiento → datos>gtm).
- **Endpoint** estado: `GET /api/state` (último estado en memoria del proceso de ejemplo).

## 2. Conceptos
- **<PERSON>o con estado (LangGraph)**: control determinista + checkpoints + posibilidad de *interrupts* para revisión humana.
- **Prompts** por rol: están en `src/prompts/`.
- **Gate QA/Sec**: checklist OWASP-LLM + mapeo NIST AI RMF en `SECURITY_GATE.md`.
- **ADR**: decisiones arquitectónicas clave en `ADR/`.
- **Plan GTM**: plantilla en `GTM/plan_template.md`.

## 3. Vibe Coding (Augment Code) — Plan de arranque
1. Crear `feature/idea-<slug>`.
2. Rellenar `POST /api/run` con tu idea inicial.
3. Revisar estado `prototipo` y `backlog` generados.
4. Completar `arquitectura mínima` y seguir pasos del Constructor.
5. Pasar por **Gate QA/Sec** → corregir bloqueantes.
6. Preparar lanzamiento con plantilla GTM.

## 4. Tests & CI
```bash
pytest -q
```
CI (GitHub Actions) corre tests de humo.

## 5. Docker
```bash
docker build -t sal-3-2 .
docker run -p 8000:8000 --env-file .env sal-3-2
```

## 6. Roles SAL-3+2
- **Orquestador con estado**: decide siguiente nodo y registra `tldr`.
- **Diseñador de Producto**: personas, journey, core copy, backlog (épicas/HU/criterios).
- **Constructor**: ADRs críticos, contratos API, esquema datos, plan de implementación + tests.
- **QA & Seguridad (Gate)**: pruebas funcionales/no-funcionales + checklist LLM.
- **Datos & Go-To-Market**: instrumentación, KPIs, experimento A/B y plan 4 semanas.

## 7. Licencia
MIT