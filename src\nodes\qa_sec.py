from src.models.state import <PERSON>stado

def node_qa_sec(state: Estado) -> Estado:
    # Simulación de checks (reemplazar por pruebas reales)
    hallazgos = []
    bloqueante = False

    # Ejemplo de hallazgo menor
    # hallazgos.append({"id": "SEC-001", "severidad": "Media", "desc": "Validar longitudes en payload"})

    reporte = {"bloqueante": bloqueante, "hallazgos": hallazgos, "fixes_priorizados": []}

    tldr = state.get("tldr", [])
    tldr.append("QA/Sec: sin bloqueantes (listo para lanzar)")

    return {**state, "reporte_qa": reporte, "tldr": tldr}