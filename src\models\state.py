from typing import TypedDict, Dict, Any, List

class Estado(TypedDict, total=False):
    idea: str
    constitucion: Dict[str, Any]
    prototipo: Dict[str, Any]
    backlog: Dict[str, Any]
    arquitectura: Dict[str, Any]
    artefactos: Dict[str, Any]
    reporte_qa: Dict[str, Any]
    plan_launch: Dict[str, Any]
    metricas: Dict[str, Any]
    tldr: List[str]

# Estado inicial mínimo
INITIAL_STATE: Estado = {
    "idea": "",
    "tldr": []
}