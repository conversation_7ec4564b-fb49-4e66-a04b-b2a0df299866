# Gate de QA & Seguridad (extracto operativo)

## Funcional
- <PERSON><PERSON> feli<PERSON>, bordes y manejo de errores consistente.

## No-funcional
- Latencia básica (objetivo: p95 ≤ 300ms en rutas críticas de MVP).
- Manejo de fallos y timeouts.

## Seguridad LLM/Aplicativa (checklist)
- Validación/saneamiento de entradas (regex/plantillas).
- Man<PERSON><PERSON> seguro de salidas (no ejecutar comandos generados por el modelo).
- Agencia controlada: límites en el uso de herramientas.
- Logs sin PII; secreto en variables de entorno.

## Reporte
- `bloqueante: true|false` + hallazgos con severidad + fixes priorizados.