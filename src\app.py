import os
from fastapi import FastAP<PERSON>
from dotenv import load_dotenv
from src.models.schemas import RunInput, RunOutput
from src.models.state import INITIAL_STATE
from src.graph.graph import build_graph

load_dotenv()
app = FastAPI(title="SAL-3-2")
_graph = build_graph()
_last_state = None

@app.get("/")
def root():
    return {"ok": True, "service": "sal-3-2"}

@app.get("/api/state")
def state():
    return {"state": _last_state or INITIAL_STATE}

@app.post("/api/run", response_model=RunOutput)
def run(input: RunInput):
    global _last_state
    state = {**INITIAL_STATE, "idea": input.idea}
    # Ejecutar el flujo completo de una vez (invoke)
    result = _graph.invoke(state)
    _last_state = result
    return {"state": result}