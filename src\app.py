import os
import traceback
from fastapi import FastAPI, HTTPException
from dotenv import load_dotenv
from src.models.schemas import RunInput, RunOutput
from src.models.state import INITIAL_STATE
from src.graph.graph import build_graph

load_dotenv()
app = FastAPI(title="SAL-3-2")
_graph = build_graph()
_last_state = None

@app.get("/")
def root():
    return {"ok": True, "service": "sal-3-2"}

@app.get("/api/state")
def state():
    return {"state": _last_state or INITIAL_STATE}

@app.post("/api/run", response_model=RunOutput)
def run(input: RunInput):
    global _last_state
    try:
        print(f"Received request with idea: {input.idea[:100]}...")
        state = {**INITIAL_STATE, "idea": input.idea}
        # Ejecutar el flujo completo de una vez (invoke)
        config = {"configurable": {"thread_id": "api-thread"}}
        print("Invoking graph...")
        result = _graph.invoke(state, config=config)
        print("Graph execution completed successfully")
        _last_state = result
        return {"state": result}
    except Exception as e:
        print(f"Error in /api/run: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))