from src.models.state import Estado

def node_data_gtm(state: Estado) -> Estado:
    metricas = {
        "eventos": ["booking_created", "msg_sent", "booking_attended"],
        "kpis": ["activacion_d7", "conversion"],
        "experimento_ab": "Texto mensaje bienvenida A vs B",
    }

    tldr = state.get("tldr", [])
    tldr.append("Datos&GTM: KPIs y experimento A/B definidos")

    return {**state, "metricas": metricas, "tldr": tldr}