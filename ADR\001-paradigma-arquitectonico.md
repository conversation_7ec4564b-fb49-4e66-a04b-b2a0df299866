# ADR-001 — Paradigma Arquitectónico

**Contexto**: MVP con presupuesto bajo, equipo pequeño, time-to-value corto.  
**Opciones**: Monolito Modular / Serverless / Microservicios.  
**Decisión**: <PERSON><PERSON><PERSON> (FastAPI) + jobs programados.  
**Justificación**: menor complejidad operativa y costo; facilita migración posterior.  
**Consecuencias**: vigilar acoplamientos; preparar interfaces limpias para extraer servicios.