Metadata-Version: 2.1
Name: langchain-core
Version: 0.3.76
Summary: Building applications with LLMs through composability
License: MIT
Project-URL: Source Code, https://github.com/langchain-ai/langchain/tree/master/libs/core
Project-URL: Release Notes, https://github.com/langchain-ai/langchain/releases?q=tag%3A%22langchain-core%3D%3D0%22&expanded=true
Project-URL: repository, https://github.com/langchain-ai/langchain
Requires-Python: >=3.9
Requires-Dist: langsmith>=0.3.45
Requires-Dist: tenacity!=8.4.0,<10.0.0,>=8.1.0
Requires-Dist: jsonpatch<2.0,>=1.33
Requires-Dist: PyYAML>=5.3
Requires-Dist: typing-extensions>=4.7
Requires-Dist: packaging>=23.2
Requires-Dist: pydantic>=2.7.4
Description-Content-Type: text/markdown

# 🦜🍎️ LangChain Core

[![PyPI - License](https://img.shields.io/pypi/l/langchain-core?style=flat-square)](https://opensource.org/licenses/MIT)
[![PyPI - Downloads](https://img.shields.io/pepy/dt/langchain-core)](https://pypistats.org/packages/langchain-core)

## Quick Install

```bash
pip install langchain-core
```

## What is it?

LangChain Core contains the base abstractions that power the the LangChain ecosystem.

These abstractions are designed to be as modular and simple as possible.

The benefit of having these abstractions is that any provider can implement the required interface and then easily be used in the rest of the LangChain ecosystem.

For full documentation see the [API reference](https://python.langchain.com/api_reference/core/index.html).

## ⛰️ Why build on top of LangChain Core?

The LangChain ecosystem is built on top of `langchain-core`. Some of the benefits:

- **Modularity**: We've designed Core around abstractions that are independent of each other, and not tied to any specific model provider.
- **Stability**: We are committed to a stable versioning scheme, and will communicate any breaking changes with advance notice and version bumps.
- **Battle-tested**: Core components have the largest install base in the LLM ecosystem, and are used in production by many companies.

## 1️⃣ Core Interface: Runnables

The concept of a `Runnable` is central to LangChain Core – it is the interface that most LangChain Core components implement, giving them

- A common invocation interface (`invoke()`, `batch()`, `stream()`, etc.)
- Built-in utilities for retries, fallbacks, schemas and runtime configurability
- Easy deployment with [LangGraph](https://github.com/langchain-ai/langgraph)

For more check out the [`Runnable` docs](https://python.langchain.com/docs/concepts/runnables/). Examples of components that implement the interface include: Chat Models, Tools, Retrievers, and Output Parsers.

## 📕 Releases & Versioning

As `langchain-core` contains the base abstractions and runtime for the whole LangChain ecosystem, we will communicate any breaking changes with advance notice and version bumps. The exception for this is anything in `langchain_core.beta`. The reason for `langchain_core.beta` is that given the rate of change of the field, being able to move quickly is still a priority, and this module is our attempt to do so.

Minor version increases will occur for:

- Breaking changes for any public interfaces NOT in `langchain_core.beta`

Patch version increases will occur for:

- Bug fixes
- New features
- Any changes to private interfaces
- Any changes to `langchain_core.beta`

## 💁 Contributing

As an open-source project in a rapidly developing field, we are extremely open to contributions, whether it be in the form of a new feature, improved infrastructure, or better documentation.

For detailed information on how to contribute, see the [Contributing Guide](https://python.langchain.com/docs/contributing/).
