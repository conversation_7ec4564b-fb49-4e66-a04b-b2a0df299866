from src.models.state import Estado

def node_intake(state: Estado) -> Estado:
    idea = state.get("idea", "").strip()
    assert idea, "'idea' no puede estar vacía"

    constitucion = {
        "mision": f"Convertir la idea '{idea}' en un MVP validable",
        "principios": ["Frugal & Open", "Seguridad por defecto", "Human-in-the-loop"],
        "restricciones": ["Presupuesto bajo", "Tiempo a valor corto"],
        "kpis": ["TTPV", "Conversión MVP"],
    }

    tldr = state.get("tldr", [])
    tldr.append("Intake: misión, principios y KPIs definidos")

    return {**state, "constitucion": constitucion, "tldr": tldr}